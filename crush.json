{"$schema": "https://charm.land/crush.json", "providers": {"deepseek": {"type": "openai", "base_url": "https://cow.rip/v1", "api_key": "sk-qrd-0a61705255376e6bbcc4fa3e112a0dbb420134ddc1829289", "models": [{"id": "anthropic/claude-opus-4-20250514-thinking", "name": "Claude <PERSON> 4 Thinking", "context_window": 200000, "default_max_tokens": 32000, "can_reason": true, "supports_attachments": true, "has_reasoning_efforts": true}, {"id": "anthropic/claude-sonnet-4-20250514-thinking", "name": "Claude <PERSON> 4 Thinking", "context_window": 200000, "default_max_tokens": 32000, "can_reason": true, "supports_attachments": true, "has_reasoning_efforts": true}, {"id": "anthropic/claude-opus-4-20250514", "name": "<PERSON> 4", "context_window": 200000, "default_max_tokens": 32000, "supports_attachments": true}, {"id": "anthropic/claude-sonnet-4-20250514", "name": "<PERSON> 4", "context_window": 200000, "default_max_tokens": 32000, "supports_attachments": true}, {"id": "google/gemini-2.5-pro-preview-06-05", "name": "Gemini 2.5 Pro Preview 06-05", "context_window": 1048576, "default_max_tokens": 65536, "supports_attachments": true, "can_reason": true}, {"id": "mistral/devstral-small-2507", "name": "Mistral Devstral Small 2507", "context_window": 128000, "default_max_tokens": 128000}, {"id": "moonshot/kimi-dev-72b", "name": "Moon<PERSON> <PERSON><PERSON> 72B", "context_window": 131072, "default_max_tokens": 131072}, {"id": "moonshot/kimi-thinking", "name": "Moonshot <PERSON>i <PERSON>", "context_window": 131072, "default_max_tokens": 131072, "can_reason": true}, {"id": "openai/o3", "name": "OpenAI O3", "context_window": 200000, "default_max_tokens": 100000, "supports_attachments": true, "can_reason": true}, {"id": "openai/o3-mini", "name": "OpenAI O3 Mini", "context_window": 200000, "default_max_tokens": 100000, "supports_attachments": true, "can_reason": true}, {"id": "openai/o4-mini", "name": "OpenAI O4 Mini", "context_window": 200000, "default_max_tokens": 100000, "supports_attachments": true, "can_reason": true}, {"id": "qwen/qwen3-235b-a22b-instruct", "name": "Qwen3 235B A22B Instruct", "context_window": 262144, "default_max_tokens": 131072}, {"id": "qwen/qwen3-235b-a22b-thinking", "name": "Qwen3 235B A22B Thinking", "context_window": 262144, "default_max_tokens": 131072, "can_reason": true}, {"id": "xai/grok-4", "name": "XAI Grok 4", "context_window": 256000, "default_max_tokens": 64000, "can_reason": true}]}}}